"use client";
import { Loading } from "@/app/components/common/loading";
import {
  closePage,
  setStatusBar,
  taskResourceLoadedHandler,
} from "@/app/utils/device";
import IconBack from "@/public/icons/back.svg";
import { useSignal } from "@preact-signals/safe-react";
import ResourcePreview from "@repo/core/components/resource-preview/layout";
import { getStorageItem, setStorageItem } from "@repo/lib/utils/local-storage";
import { useSearchParams } from "next/navigation";
import { FC, Suspense, useCallback, useEffect, useMemo } from "react";
import { toast } from "../components/common/toast";
import { IconButton } from "../components/guide/guide-buttons";
import { useFixed } from "../hooks/use-fixed";
import {
  useResourceDocumentRefreshToken,
  useResourcePreviewInfo,
} from "../models/resource-preview";
import { useClientContext } from "../providers/client-provider";

const Content: FC = () => {
  const searchParams = useSearchParams();
  const taskId = searchParams.get("taskId");
  const resourceId = searchParams.get("resourceId");

  if (!taskId || !resourceId)
    throw new Error(
      `资源预览页面 taskId ${taskId || "不存在"} resourceId ${resourceId || "不存在"}`
    );
  const { studentUserInfo } = useClientContext();
  const token = useSignal<{ accessToken: string; refreshToken: string } | null>(
    null
  );
  const key = `${resourceId}-${taskId}`;
  const lastTime = useFixed(getStorageItem(key, { value: 0 }));
  const { data, isLoading, error } = useResourcePreviewInfo({
    resourceId,
    taskId,
  });
  const { trigger } = useResourceDocumentRefreshToken();
  useEffect(() => {
    if (data?.webOffice) {
      token.value = {
        accessToken: data.webOffice.accessToken,
        refreshToken: data.webOffice.refreshToken,
      };
    }
  }, [data, token]);
  useEffect(() => {
    if (lastTime.value !== 0) toast.show("已为您定位到上次播放位置");
  }, [lastTime.value]);
  const resourceData = useMemo(
    () => (data ? [{ ...data, webOffice: data.webOffice || undefined }] : []),
    [data]
  );

  const getResourceDocumentRefreshToken = useCallback(async () => {
    if (!token.value) throw new Error("没有token");
    const res = await trigger({
      ...token.value,
      taskId: Number(taskId),
    });
    token.value = { ...res };
    return {
      token: res.accessToken,
      timeout:
        Date.parse(res.accessTokenExpiredTime) - Date.now() - 5 * 60 * 1000,
    };
  }, [taskId, token, trigger]);
  if (error) throw error;
  return (
    <div className="relative h-screen w-full">
      <div className="pointer-events-none absolute top-8 z-10 flex h-11 w-full flex-row items-center justify-between px-8">
        <IconButton
          icon={<IconBack />}
          onClick={closePage}
          className="!bg-transparent !shadow-none !outline-0"
        />
      </div>
      <ResourcePreview
        visible
        showClose={false}
        resourcesData={resourceData}
        isFetchingResourcesData={isLoading}
        getResourceDocumentRefreshToken={getResourceDocumentRefreshToken}
        onPreview={() => {
          taskResourceLoadedHandler();
        }}
        tag="学生端"
        subtag="资源预览"
        userId={String(studentUserInfo?.userId ?? "")}
        onTimeUpdate={(value) => setStorageItem(key, { value })}
        startTime={lastTime.value}
      />
    </div>
  );
};

export default function Page() {
  useEffect(() => {
    setStatusBar({
      eventType: "setStatusBarVisibility",
      isVisible: false,
    });
  }, []);

  return (
    <Suspense
      fallback={
        <div className="flex h-screen items-center justify-center">
          <Loading />
        </div>
      }
    >
      <Content />
    </Suspense>
  );
}
