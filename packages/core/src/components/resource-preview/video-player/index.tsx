import { cn } from "@repo/ui/lib/utils";
import type { VolcenginePlayer } from "../../volcengine-video/volcengine-video";
import { VideoControllerView } from "./video-controller-view";
import { VideoPlayerView, VideoPlayerViewProps } from "./video-player-view";
import { VideoViewContextProvider } from "./video-view-context";

import { memo } from "react";
import "./overwrite.css";
import VideoTitle from "./video-title";

export type VideoPlayerProps = {
  src: string;
  title?: string;
  defaultPlayRate?: number;
  userId?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onError?: (error?: unknown) => void;
  onCanPlay?: () => void;
  refVolcenginePlayer?: React.RefObject<VolcenginePlayer | null>;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;

  tag: VideoPlayerViewProps["tag"];
  subtag: string; // 需要根据情况拆分，目前推荐`${页面/场景}${用途}`的格式，方便线上查卡顿

  startTime?: number;
  onTimeUpdate?: (time: number) => void;
};

export default memo(function VideoPlayer({
  className,
  style,
  title,
  tag,
  subtag,
  startTime,

  ...props
}: VideoPlayerProps) {
  return (
    <div className={cn("relative flex", className)} style={style}>
      <VideoViewContextProvider {...props} startTime={startTime}>
        <VideoTitle title={title} />
        <VideoPlayerView
          className="flex h-full w-full items-center justify-center"
          tag={tag}
          subtag={subtag}
          startTime={startTime}
        />
        <VideoControllerView />
      </VideoViewContextProvider>
    </div>
  );
});
